--- general overview
graph TB
    %% External Access Layer
    subgraph "External Access"
        Browser[Web Browser]
        TraefikLB[Traefik Load Balancer<br/>qw-mono-dev-traefik<br/>Port 80, 8080]
    end

    %% Frontend Layer
    subgraph "Frontend Layer"
        Angular[Angular Frontend<br/>qw-webui<br/>Port 4200]
    end

    %% Backend Services Layer
    subgraph "Backend Services"
        Runtime[Runtime Container<br/>qw-mono-dev-runtime<br/>Falcon + Gunicorn<br/>Port 8000]
        MCP[MCP Server Container<br/>qw-mono-dev-mcp<br/>FastMCP Server<br/>Port 8000]
        Worker[Worker Container<br/>qw-mono-dev-worker<br/>Celery Worker]
    end

    %% Infrastructure Services
    subgraph "Infrastructure Services"
        Postgres[(PostgreSQL<br/>qw-mono-dev-postgres<br/>Port 5432)]
        MinIO[(MinIO S3<br/>qw-mono-dev-minio<br/>Port 9000, 9001)]
        RabbitMQ[(RabbitMQ<br/>qw-mono-dev-rabbitmq<br/>Port 5672, 15672)]
        Keycloak[Keycloak IAM<br/>qw-mono-dev-keycloak<br/>Port 8080]
        Collabora[Collabora Office<br/>qw-mono-dev-collabora<br/>Port 9980]
    end

    %% Network Connections
    Browser --> TraefikLB
    TraefikLB --> Angular
    TraefikLB --> Runtime
    TraefikLB --> MCP
    TraefikLB --> Postgres
    TraefikLB --> MinIO
    TraefikLB --> RabbitMQ
    TraefikLB --> Keycloak
    TraefikLB --> Collabora

    %% Frontend to Backend
    Angular -->|HTTP API Calls| Runtime
    Angular -->|Agent Requests| Runtime

    %% Backend Internal Communication
    Runtime -->|Database Queries| Postgres
    Runtime -->|File Storage| MinIO
    Runtime -->|Task Queue| RabbitMQ
    Runtime -->|Authentication| Keycloak
    Runtime -->|Document Editing| Collabora
    Runtime -->|MCP Tool Calls| MCP

    %% MCP Server Communication
    MCP -->|Internal API Calls| Runtime
    MCP -->|Session Authentication| Runtime

    %% Worker Communication
    Worker -->|Task Processing| RabbitMQ
    Worker -->|Database Access| Postgres
    Worker -->|File Processing| MinIO

    %% Container Network
    subgraph "Docker Network: traefik"
        Runtime
        MCP
        Worker
        Angular
        Postgres
        MinIO
        RabbitMQ
        Keycloak
        Collabora
        TraefikLB
    end

    %% Styling
    classDef container fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef database fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef external fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef network fill:#fff3e0,stroke:#e65100,stroke-width:2px

    class Runtime,MCP,Worker,Angular container
    class Postgres,MinIO,RabbitMQ database
    class Browser,TraefikLB external


--- detailed view for the agent/request workflow
sequenceDiagram
    participant FE as Angular Frontend
    participant RT as Runtime Container<br/>(Falcon + Gunicorn)
    participant AG as Agent Service
    participant MCP as MCP Server Container<br/>(FastMCP)
    participant API as Internal API<br/>(Runtime Container)
    participant DB as PostgreSQL
    participant S3 as MinIO S3

    Note over FE,S3: Agent Request Processing Flow

    %% 1. Frontend initiates request
    FE->>RT: POST /api/v1/agent/process-prompt<br/>Cookie: session=token<br/>Body: {prompt, context}

    %% 2. Runtime processes request
    RT->>RT: ProcessAgentPromptController<br/>Authentication & Authorization
    RT->>AG: AgentService.process_prompt()<br/>session_token extracted from cookie

    %% 3. Agent service processes
    AG->>AG: RouterAgent.process()<br/>Context: {session_id, user_context, session_token}

    %% 4. Agent calls MCP tools
    Note over AG,MCP: MCP Client Connection
    AG->>MCP: FastMCP Client<br/>call_tool(tool_name, kwargs)<br/>URL: http://qw-mono-dev-mcp:8000/mcp

    %% 5. MCP server processes tool call
    MCP->>MCP: MCPServerService<br/>Tool execution (manual/auto-generated)

    %% 6. MCP makes internal API calls
    Note over MCP,API: Internal API Communication
    MCP->>API: HTTP Request to Internal API<br/>Cookie: session=token<br/>URL: http://qw-mono-dev-runtime:8000/api/v1/*

    %% 7. Internal API processes request
    API->>DB: Database Query<br/>(SQLAlchemy)
    DB-->>API: Query Results
    API->>S3: File Operations<br/>(if needed)
    S3-->>API: File Data
    API-->>MCP: JSON Response

    %% 8. MCP returns tool result
    MCP-->>AG: Tool execution result<br/>(formatted string)

    %% 9. Agent generates response
    AG->>AG: Generate AgentPromptResponse<br/>{message, actions, session_id}
    AG-->>RT: AgentPromptResponse

    %% 10. Runtime returns to frontend
    RT-->>FE: HTTP 200 OK<br/>JSON: {message, actions, session_id}

    %% Styling
    Note over FE,S3: Key Components:
    Note over FE,S3: • Falcon framework for API routing
    Note over FE,S3: • FastMCP for MCP server/client communication
    Note over FE,S3: • Session-based authentication via cookies
    Note over FE,S3: • Container-to-container network communication

--- detailed api endpoint
graph TB
    %% Main Application Entry
    subgraph "Application Entry Point"
        Entry[entrypoint.py]
        Server[qw_mono.server.py<br/>QwMonoWSGIApp<br/>Gunicorn WSGI]
        App[qw_mono.app.py<br/>QwMonoApp.build()]
    end

    %% Falcon Framework Layer
    subgraph "Falcon Framework Layer"
        FalconApp[falcon.App<br/>WSGI Application]
        OpenApiRouting[qw_falcon_openapi<br/>OpenApiRouting<br/>Route Management]
        Controllers[OpenApiPathController<br/>Route Handlers]
    end

    %% API Module Structure
    subgraph "API Modules (qw_pfoertner.api)"
        Internal["/api/internal/*<br/>• get_health<br/>• get_info"]
        Session["/api/v1/session/*<br/>• login/init<br/>• login/conclude<br/>• logout/init<br/>• session info"]
        Agent["/api/v1/agent/*<br/>• process-prompt"]
        FileRes["/api/v1/file-resource/*<br/>• upload/download<br/>• drawing analysis<br/>• material certificates"]
        Inspection["/api/v1/inspection/*<br/>• plans<br/>• results<br/>• actions"]
        Material["/api/v1/material/*<br/>• list materials<br/>• get material"]
        Order["/api/v1/order/*<br/>• orders<br/>• order lines<br/>• tasks"]
        User["/api/v1/user/*<br/>• whoami<br/>• get user<br/>• list users"]
        Tenant["/api/v1/tenant/*<br/>• get tenant<br/>• tenant details<br/>• list tenants"]
        Search["/api/v1/search/*<br/>• search matches"]
        Chat["/api/v1/chat/*<br/>• messages<br/>• add/edit messages"]
        WOPI["/api/v1/wopi/*<br/>• file operations<br/>• document editing"]
        Tolerance["/api/v1/tolerance/*<br/>• standards<br/>• lookup"]
    end

    %% Service Layer
    subgraph "Service Layer (qw_trunk.service)"
        AgentSvc[AgentService<br/>PydanticAI Integration]
        FileSvc[FileService<br/>S3 + Database]
        InspectionSvc[InspectionService<br/>Plans & Results]
        MaterialSvc[MaterialService<br/>Material Management]
        OrderSvc[OrderService<br/>Order Management]
        UserSvc[UserService<br/>User Management]
        ChatSvc[ChatService<br/>Chat Management]
        SearchSvc[SearchService<br/>Full-text Search]
    end

    %% Authentication & Authorization
    subgraph "Auth Layer"
        SessionSvc[SessionService<br/>Session Management]
        IAM[IdentityAccessManagement<br/>Keycloak Integration]
        Policy[QwPolicy<br/>Authorization Rules]
    end

    %% Data Layer
    subgraph "Data Layer"
        Database[(PostgreSQL<br/>SQLAlchemy ORM<br/>qw_monodb.table)]
        S3Storage[(MinIO S3<br/>File Storage<br/>qw_basic_s3)]
        MessageQueue[(RabbitMQ<br/>Task Queue<br/>Celery)]
    end

    %% Flow Connections
    Entry --> Server
    Server --> App
    App --> FalconApp
    FalconApp --> OpenApiRouting
    OpenApiRouting --> Controllers

    %% API to Services
    Controllers --> Internal
    Controllers --> Session
    Controllers --> Agent
    Controllers --> FileRes
    Controllers --> Inspection
    Controllers --> Material
    Controllers --> Order
    Controllers --> User
    Controllers --> Tenant
    Controllers --> Search
    Controllers --> Chat
    Controllers --> WOPI
    Controllers --> Tolerance

    %% Services Layer
    Agent --> AgentSvc
    FileRes --> FileSvc
    Inspection --> InspectionSvc
    Material --> MaterialSvc
    Order --> OrderSvc
    User --> UserSvc
    Chat --> ChatSvc
    Search --> SearchSvc

    %% Auth Integration
    Session --> SessionSvc
    SessionSvc --> IAM
    SessionSvc --> Policy

    %% Data Access
    AgentSvc --> Database
    FileSvc --> Database
    FileSvc --> S3Storage
    InspectionSvc --> Database
    MaterialSvc --> Database
    OrderSvc --> Database
    UserSvc --> Database
    ChatSvc --> Database
    SearchSvc --> Database

    %% Background Processing
    FileSvc --> MessageQueue
    InspectionSvc --> MessageQueue

    %% Styling
    classDef entry fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef framework fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef api fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef service fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef auth fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef data fill:#e0f2f1,stroke:#00695c,stroke-width:2px

    class Entry,Server,App entry
    class FalconApp,OpenApiRouting,Controllers framework
    class Internal,Session,Agent,FileRes,Inspection,Material,Order,User,Tenant,Search,Chat,WOPI,Tolerance api
    class AgentSvc,FileSvc,InspectionSvc,MaterialSvc,OrderSvc,UserSvc,ChatSvc,SearchSvc service
    class SessionSvc,IAM,Policy auth
    class Database,S3Storage,MessageQueue data


--- docker compose summary
services:
  postgres:          # Database (Port 5432)
  minio:            # S3-compatible storage (Ports 9000, 9001)
  keycloak:         # Identity & Access Management (Port 8080)
  collabora:        # Document editing service (Port 9980)
  runtime:          # Main backend application (Port 4200)
  rabbitmq:         # Message queue (Ports 5672, 15672)
  mcp:              # MCP server (Port 8000)
  worker:           # Background task processor
  traefik:          # Load balancer & reverse proxy (Ports 80, 8080)


Single Docker Network: All containers communicate via the traefik network
Service Discovery: Containers use hostname-based communication (e.g., qw-mono-dev-postgres)
External Access: Traefik provides reverse proxy with domain-based routing (*.docker.localhost)
Key Communication Patterns
Frontend ↔ Backend: HTTP API calls through Traefik
Backend ↔ MCP: Container-to-container HTTP communication
MCP ↔ Internal API: Loopback calls to the same backend via different container
Backend ↔ Infrastructure: Direct database, S3, and message queue connections


--- API Endpoint Structure
Internal APIs: /api/internal/* (health, info)
Session Management: /api/v1/session/* (login, logout, session info)
Agent Processing: /api/v1/agent/process-prompt
Business Logic: File resources, inspections, materials, orders, users, tenants
Integration: WOPI (document editing), search, chat, tolerances

Gunicorn WSGI serves the Falcon application
OpenApiRouting handles route matching and OpenAPI spec generation
Controllers implement business logic with dependency injection
Service Layer provides business operations
Data Layer handles database and S3 operations

Dependency Injection: Services configured via factory methods
OpenAPI Integration: Automatic spec generation and Swagger UI
Authentication: Session-based auth with Keycloak integration
Error Handling: Structured error responses
Multipart Support: File upload handling


--- Docker Infrastructure
Development Environment: Full source code mounted for hot reloading
Production Environment: Optimized builds with minimal attack surface
Service Dependencies: Explicit dependency management via depends_on
Health Checks: Built-in health monitoring for critical services
