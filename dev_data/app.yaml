mono_db:
  create_tables_if_not_exist: yes
  schema_verification:
    type: skip
  backend:
    type: postgres
    host: postgres.docker.localhost
    port: 5432
    database_name: "qw_mono"
    username: postgres_admin
    password: postgres_admin
    pool_size: 10
    pool_max_overflow: 15
    pool_timeout_seconds: 10
#  backend:
#    type: sqlite
#    path: /tmp/qw-store.db

mono_s3:
  create_buckets_if_not_exist: yes
  backend:
    type: minio
    host: minio.docker.localhost
    access_key: minio_admin
    secret_key: minio_admin
    secure: no
#  backend:
#    type: local
#    path: /tmp/qw-store-s3.json

mono_pfoertner:
  iam:
    type: "keycloak"
    host: "http://keycloak.docker.localhost"
    initial_healthcheck:
      timeout_in_seconds: 5
      retry_count: 12
    realm: "qualiwise"
    openid_redirect_host: "http://app.docker.localhost"
    openid_redirect_route: "/api/v1/session/login/conclude"
    openid_client_id: "qualiwise-pfoertner"
    openid_client_secret: "4qYgrCNSoxLc3I73okdUZr69BRYUlJ8u"
  tkn_gateway_salt: 31
  session_settings: {}
  wopi_auth_settings:
    jws_oct_key: "rDC09N0a8bzg9jzf"
    jws_alg: "HS256"
  policy_settings: {}
  settings:
    include_spec_route: yes

mono_trunk:
  chat_db_settings: {}
  drawing_settings: {}
  wopi_settings:
    wopi_client_host: "http://collabora.docker.localhost"
  api_keys:
    claude_api_key: "************************************************************************************************************"
    groq_api_key: "********************************************************"
    openai_key: "********************************************************"
    gemini_api_key: "AIzaSyDpNeuZ2i--dS6c5pEcDCrjqp_xQFNsUCo"

mono_worker:
  celery_broker_settings:
    type: rabbitmq
    host: qw-mono-dev-rabbitmq # rabbitmq.docker.localhost did not work / continue to use internal docker dns name
    port: 5672
    user: rabbitmq_admin
    password: rabbitmq_admin
  celery_worker_settings:
    concurrency: 1
    max_tasks_per_child: 10
    loglevel: info
  api_keys:
    claude_api_key: "************************************************************************************************************"
    groq_api_key: "********************************************************"
    openai_key: "********************************************************"
    gemini_api_key: "AIzaSyDpNeuZ2i--dS6c5pEcDCrjqp_xQFNsUCo"

mono_agent_service:
  runtime_service_url: "http://qw-mono-dev-runtime:8000"
  agent_settings:
    model_name: "gpt-4o"
    temperature: 0.2
    timeout: 30.0
    enable_specialized_agents: false
  performance_settings:
    max_concurrent_requests: 10
    request_timeout: 300.0
  auth_settings:
    session_validation_timeout: 10.0
    session_cache_ttl: 300
  api_keys:
    openai_key: "********************************************************"

mono_mcp_server:
  enabled: true
  host: "0.0.0.0"
  port: 8000
  transport: "stdio"
  api_base_url: "http://qw-mono-dev-runtime:8000"
  openapi_spec_path: "qw-mono/docs/qw-pfoertner-openapi.yaml"
  enable_auto_generated_tools: true
  enable_manual_tools: true
  internal_token: "internal-mcp-token-dev"

tenants:
  directory: ${DIR}/tenants
  validate_on_load: yes

logging:
  level: "INFO"
  logfire:
    enabled: true
    token: "pylf_v1_eu_9gCh6MS1NVshPb8FpSLHdywh9G1Ywq4xFtXMsmqxpmvN"
    instrument_pydantic_ai: true
