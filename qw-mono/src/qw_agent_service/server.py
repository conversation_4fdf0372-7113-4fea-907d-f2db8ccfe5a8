"""FastAPI Agent Service Server."""
import os
from pathlib import Path
from typing import Optional
import uvicorn

from qw_config.loader import load_config
from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.config_models import QwAgentServiceMonoConfig
from qw_agent_service.config import AgentServiceConfig


class AgentServiceApp:
    """FastAPI Agent Service Application."""

    def __init__(
        self,
        config: AgentServiceConfig,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the agent service application.
        """
        self.config = config
        self.lf = lf
        self.logger = lf.get_logger(__name__)

    def run(self) -> None:
        """
        Run the FastAPI agent service.
        """
        # Import here to avoid circular imports and ensure proper initialization
        from qw_agent_service.main import app
        from qw_agent_service.auth import initialize_auth_service

        # Initialize authentication service with config
        initialize_auth_service(self.config, self.lf)

        # Store config in app state for access by endpoints
        app.state.config = self.config
        app.state.log_factory = self.lf

        # Configure uvicorn
        uvicorn_config = {
            "app": app,
            "host": "0.0.0.0",
            "port": 8000,
            "log_level": "info",
            "reload": os.getenv("ENVIRONMENT", "development") == "development",
            "access_log": True,
        }

        self.logger.info("Starting uvicorn server with FastAPI agent service")
        uvicorn.run(**uvicorn_config)

    @classmethod
    def from_config(
        cls,
        config_path: Path,
        config_overwrite_path: Optional[Path] = None,
        lf: LogFactory = NO_LOG_FACTORY,
    ) -> "AgentServiceApp":
        """
        Create agent service app from configuration files.
        """
        logger = lf.get_logger(__name__)
        logger.info(f"Initializing agent service from config: {config_path}")

        # Load configuration
        config = AgentServiceConfig.from_config(config_path, config_overwrite_path)

        return cls(config=config, lf=lf)
