"""Agent service implementation for FastAPI."""
import asyncio
from typing import Dict, Any, Optional
from uuid import uuid4

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.models import AgentPromptRequest, AgentPromptResponse, AgentAction
from qw_agent_service.config import AgentServiceConfig
from qw_agent_service.minimal_agent import MinimalRouterAgent
from qw_agent_service.mcp_enhanced_agent import MCPEnhancedAgent
from qw_trunk.service.agent.session.session_service import SessionService


class AgentService:
    """
    FastAPI Agent Service implementation with minimal dependencies.
    Handles agent prompt processing with session management.
    """

    def __init__(
        self,
        config: AgentServiceConfig,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the agent service.
        """
        self.config = config
        self.logger = lf.get_logger(__name__)
        self.lf = lf

        # Initialize session manager (in-memory for now)
        self.session_manager = SessionService(lf=lf)

        # Initialize router agent (can be minimal or MCP-enhanced)
        self.router_agent: Optional[MinimalRouterAgent] = None
        self.mcp_agent: Optional[MCPEnhancedAgent] = None
        self.agent_available = False
        self.use_mcp = config.use_mcp_integration if hasattr(config, 'use_mcp_integration') else False

    async def initialize(self) -> None:
        """
        Initialize the agent service asynchronously.
        """
        try:
            self.logger.info("Initializing agent service...")

            # Try to initialize MCP-enhanced agent first if MCP is available
            mcp_server_url = "http://qw-mono-dev-mcp-server:8000"  # Internal container URL

            try:
                self.logger.info("Attempting to initialize MCP-enhanced agent...")
                self.mcp_agent = MCPEnhancedAgent.from_config(
                    self.config,
                    mcp_server_url=mcp_server_url,
                    lf=self.lf
                )

                if self.mcp_agent.agent_available:
                    self.agent_available = True
                    self.use_mcp = True
                    self.logger.info("MCP-enhanced agent initialized successfully")
                    return
                else:
                    self.logger.warning("MCP-enhanced agent initialization failed, falling back to minimal agent")

            except Exception as e:
                self.logger.warning(f"MCP-enhanced agent initialization failed: {e}, falling back to minimal agent")

            # Fallback to minimal router agent
            self.logger.info("Initializing minimal router agent...")
            self.router_agent = MinimalRouterAgent.from_config(self.config, self.lf)

            if self.router_agent.agent_available:
                self.agent_available = True
                self.use_mcp = False
                self.logger.info("Minimal agent service initialized successfully")
            else:
                self.logger.error("Router agent initialization failed")
                self.agent_available = False

        except Exception as e:
            self.logger.error(f"Failed to initialize agent service: {e}")
            self.agent_available = False

    async def process_prompt_async(self, request: AgentPromptRequest) -> AgentPromptResponse:
        """
        Process agent prompt asynchronously.
        """
        if not self.agent_available:
            self.logger.error("Agent service not available")
            return AgentPromptResponse(
                message="Agent service is currently unavailable. Please try again later.",
                session_id=request.session_id or uuid4(),
                actions=[]
            )

        # Get or create session
        session = self.session_manager.get_or_create_session(request.session_id)

        try:
            self.logger.info(f"Processing prompt for session: {session.session_id} (MCP: {self.use_mcp})")

            # Prepare context
            context = {
                "session_id": str(session.session_id),
                "user_context": request.context or {},
            }

            if request.context:
                self.logger.info(f"Context keys received: {list(request.context.keys())}")

            # Set session token for MCP agent if available
            if self.use_mcp and self.mcp_agent and request.context and "session_token" in request.context:
                session_token = request.context["session_token"]
                await self.mcp_agent.set_session_token(session_token)
                self.logger.info("Session token set for MCP agent")

            # Process with appropriate agent
            if self.use_mcp and self.mcp_agent:
                response_data = await self.mcp_agent.process(
                    request.prompt,
                    context=context,
                    usage=None
                )
            elif self.router_agent:
                response_data = await self.router_agent.process(
                    request.prompt,
                    context=context,
                    usage=None
                )
            else:
                raise Exception("No agent available for processing")

            # Update session activity
            self.session_manager.update_session_activity(session.session_id)

            # Build response
            return AgentPromptResponse(
                message=response_data.message,
                session_id=session.session_id,
                actions=response_data.actions
            )

        except Exception as e:
            self.logger.error(f"Error processing prompt: {e}")
            return AgentPromptResponse(
                message="Sorry, I encountered an error processing your request. Please try again.",
                session_id=session.session_id,
                actions=[]
            )

    async def cleanup(self) -> None:
        """
        Cleanup agent service resources.
        """
        self.logger.info("Cleaning up agent service...")
        self.agent_available = False

        # Cleanup MCP agent if available
        if self.mcp_agent:
            try:
                await self.mcp_agent.cleanup()
                self.logger.info("MCP agent cleaned up successfully")
            except Exception as e:
                self.logger.error(f"Error cleaning up MCP agent: {e}")

        # Additional cleanup can be added here

    @classmethod
    def from_config(cls, config: AgentServiceConfig, lf: LogFactory = NO_LOG_FACTORY) -> "AgentService":
        """
        Create AgentService from configuration.
        """
        return cls(config=config, lf=lf)
