"""FastAPI Agent Service - Main Application."""
import os
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAP<PERSON>, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.auth import get_current_user, initialize_auth_service
from qw_agent_service.service import AgentService
from qw_agent_service.models import (
    AgentPromptRequest,
    AgentPromptResponse,
    HealthResponse,
    ErrorResponse
)
from qw_shared_auth.models import AuthContext


# Global instances
agent_service: AgentService
log_factory: LogFactory


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan management."""
    global agent_service, log_factory

    # Get config and log factory from app state (set by server.py)
    config = getattr(app.state, 'config', None)
    log_factory = getattr(app.state, 'log_factory', NO_LOG_FACTORY)

    if config is None:
        # Fallback for direct uvicorn usage (development)
        from qw_agent_service.config import AgentServiceConfig
        from pathlib import Path

        config_path = Path("dev_data/app.yaml")
        if config_path.exists():
            config = AgentServiceConfig.from_config(config_path)
        else:
            raise RuntimeError("No configuration available and dev_data/app.yaml not found")

    logger = log_factory.get_logger(__name__)

    try:
        logger.info("Starting FastAPI Agent Service...")
        logger.info(f"Configuration loaded - FastAPI agent enabled: {config.use_fastapi_agent}")

        # Initialize agent service
        agent_service = AgentService.from_config(config, log_factory)
        await agent_service.initialize()

        if agent_service.agent_available:
            logger.info("Agent service startup completed successfully")
        else:
            logger.error("Agent service startup failed - agent not available")

        # Store in app state
        app.state.agent_service = agent_service
        app.state.config = config
        app.state.log_factory = log_factory

        yield

    except Exception as e:
        logger.error(f"Startup failed: {e}")
        raise
    finally:
        # Shutdown
        logger.info("Shutting down FastAPI Agent Service...")
        if 'agent_service' in locals():
            await agent_service.cleanup()
        logger.info("Shutdown completed")


# Create FastAPI application
app = FastAPI(
    title="QW Agent Service",
    description="Dedicated FastAPI service for AI agent processing",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs" if os.getenv("ENVIRONMENT", "development") == "development" else None,
    redoc_url="/redoc" if os.getenv("ENVIRONMENT", "development") == "development" else None,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://app.docker.localhost", "http://localhost:4200"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "OPTIONS"],
    allow_headers=["*"],
)


@app.exception_handler(HTTPException)
async def http_exception_handler(_: Request, exc: HTTPException):
    """Handle HTTP exceptions with structured error responses."""
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            detail=exc.detail,
            error_code=str(exc.status_code)
        ).model_dump()
    )


@app.exception_handler(Exception)
async def general_exception_handler(_: Request, exc: Exception):
    """Handle general exceptions with structured error responses."""
    logger = log_factory.get_logger(__name__) if 'log_factory' in globals() else NO_LOG_FACTORY.get_logger(__name__)
    logger.error(f"Unhandled exception: {exc}")

    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            detail="Internal server error",
            error_code="500"
        ).model_dump()
    )


@app.post("/api/v1/agent/process-prompt", response_model=AgentPromptResponse)
async def process_agent_prompt(
    request: AgentPromptRequest,
    current_user: AuthContext = Depends(get_current_user)
) -> AgentPromptResponse:
    """
    Process agent prompt with async handling.
    This endpoint maintains compatibility with the existing Falcon implementation.
    """
    logger = log_factory.get_logger(__name__)
    logger.info(f"Processing agent prompt for user: {current_user.subject}")

    try:
        # Get agent service from app state
        service: AgentService = app.state.agent_service

        # Process the prompt
        response = await service.process_prompt_async(request)

        logger.info(f"Agent prompt processed successfully for session: {response.session_id}")
        return response

    except Exception as e:
        logger.error(f"Error processing agent prompt: {e}")
        raise HTTPException(status_code=500, detail="Failed to process agent prompt")


@app.get("/health", response_model=HealthResponse)
async def health_check() -> HealthResponse:
    """Health check endpoint."""
    try:
        service: AgentService = app.state.agent_service
        return HealthResponse(
            status="healthy",
            service="agent-service",
            version="1.0.0",
            agent_available=service.agent_available
        )
    except Exception:
        return HealthResponse(
            status="unhealthy",
            service="agent-service",
            version="1.0.0",
            agent_available=False
        )


@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "QW Agent Service", "version": "1.0.0"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )
