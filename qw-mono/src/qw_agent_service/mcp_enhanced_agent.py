"""MCP-enhanced RouterAgent implementation with FastMCP client integration."""
import asyncio
from typing import Any, Dict, Optional, Union, cast, List
from uuid import UUID, uuid4

from pydantic_ai import Agent as PydanticAgent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from pydantic_ai.settings import ModelSettings
from pydantic_ai.usage import Usage
from fastmcp import Client

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.models import AgentPromptRequest, AgentPromptResponse, AgentAction
from qw_agent_service.config import AgentServiceConfig
from qw_agent_service.minimal_agent import MinimalAgentContext, MinimalAgentResponse


class MCPEnhancedAgent:
    """
    MCP-enhanced RouterAgent that can use internal API tools via FastMCP client.
    """
    
    def __init__(
        self,
        api_key: str,
        mcp_server_url: str,
        model_name: str = "gpt-4o",
        temperature: float = 0.2,
        timeout: float = 30.0,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the MCP-enhanced router agent.
        """
        self.api_key = api_key
        self.mcp_server_url = mcp_server_url
        self.model_name = model_name
        self.temperature = temperature
        self.timeout = timeout
        self.logger = lf.get_logger(__name__)
        self.lf = lf
        self.agent_available = False
        self.usage = Usage()
        
        # MCP client for tool access
        self.mcp_client: Optional[Client] = None
        self.available_tools: List[Dict[str, Any]] = []
        self.session_token: Optional[str] = None
        
        self._configure_agent()
    
    async def _setup_mcp_client(self) -> None:
        """Setup MCP client connection."""
        try:
            self.logger.info(f"Connecting to MCP server at {self.mcp_server_url}")
            self.mcp_client = Client(self.mcp_server_url)
            
            # Connect and get available tools
            await self.mcp_client.__aenter__()
            self.available_tools = await self.mcp_client.list_tools()
            
            self.logger.info(f"Connected to MCP server with {len(self.available_tools)} tools")
            
        except Exception as e:
            self.logger.error(f"Failed to setup MCP client: {e}")
            self.mcp_client = None
            self.available_tools = []
    
    def _configure_agent(self) -> None:
        """
        Configure the pydantic-ai agent with MCP tools.
        """
        try:
            # Prepare model settings
            model_settings = ModelSettings(
                temperature=self.temperature,
                timeout=self.timeout,
            )
            
            # Setup OpenAI model with provider
            provider = OpenAIProvider(api_key=self.api_key)
            
            # Extract model name without provider prefix if needed
            model_name = self.model_name
            if model_name.startswith("openai:"):
                model_name = model_name.split(":")[-1]
            
            model = OpenAIModel(model_name, provider=provider)
            
            # Create the agent with MCP tools
            self.agent = PydanticAgent[MinimalAgentContext, str](
                model=model,
                model_settings=model_settings,
                retries=2,
                system_prompt=self._get_system_prompt(),
                deps_type=MinimalAgentContext,
            )
            
            # Add MCP tools to the agent
            self._register_mcp_tools()
            
            self.agent_available = True
            self.logger.info("MCP-enhanced router agent configured successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to configure MCP-enhanced router agent: {e}")
            self.agent_available = False
    
    def _register_mcp_tools(self) -> None:
        """Register MCP tools with the pydantic-ai agent."""
        
        @self.agent.tool
        async def set_session_token(token: str) -> str:
            """Set the session token for API authentication."""
            self.session_token = token
            self.logger.info(f"Session token set: {token[:10]}...")
            
            # Call MCP server to set session token
            if self.mcp_client:
                try:
                    result = await self.mcp_client.call_tool("set_session_token", {"token": token})
                    return f"Session token configured: {result[0].text if result else 'success'}"
                except Exception as e:
                    return f"Failed to set session token: {e}"
            return "Session token stored locally (MCP client not available)"
        
        @self.agent.tool
        async def get_api_health() -> str:
            """Check the health of the internal API."""
            if not self.mcp_client:
                return "MCP client not available"
            
            try:
                result = await self.mcp_client.call_tool("get_api_health", {})
                return result[0].text if result else "No response"
            except Exception as e:
                return f"Health check failed: {e}"
        
        @self.agent.tool
        async def list_materials(tenant_id: int, limit: int = 10) -> str:
            """List materials for a specific tenant."""
            if not self.mcp_client:
                return "MCP client not available"
            
            try:
                result = await self.mcp_client.call_tool("list_materials", {
                    "tenant_id": tenant_id,
                    "limit": limit
                })
                return result[0].text if result else "No materials found"
            except Exception as e:
                return f"Failed to list materials: {e}"
        
        @self.agent.tool
        async def search_materials(tenant_id: int, query: str, limit: int = 5) -> str:
            """Search for materials by name or description."""
            if not self.mcp_client:
                return "MCP client not available"
            
            try:
                result = await self.mcp_client.call_tool("search_materials", {
                    "tenant_id": tenant_id,
                    "query": query,
                    "limit": limit
                })
                return result[0].text if result else "No materials found"
            except Exception as e:
                return f"Material search failed: {e}"
    
    def _get_system_prompt(self) -> str:
        """
        Get the system prompt for the MCP-enhanced router agent.
        """
        return """
        You are a helpful AI assistant for a quality management system with access to internal APIs.
        
        You can help users with:
        - General questions about quality management
        - Information about inspection processes
        - Guidance on material certificates and technical drawings
        - Accessing and searching materials in the system
        - Workflow assistance
        
        You have access to the following tools:
        - set_session_token: Set authentication token for API access
        - get_api_health: Check system health
        - list_materials: List materials for a tenant
        - search_materials: Search materials by query
        
        Before accessing any data, you may need to set a session token for authentication.
        Always provide helpful, accurate responses and use the available tools when appropriate.
        
        Keep your responses concise and professional.
        """
    
    async def set_session_token(self, token: str) -> None:
        """Set session token for API authentication."""
        self.session_token = token
        self.logger.info(f"Session token set for agent: {token[:10]}...")
        
        # Setup MCP client if not already done
        if not self.mcp_client:
            await self._setup_mcp_client()
        
        # Set token in MCP server
        if self.mcp_client:
            try:
                await self.mcp_client.call_tool("set_session_token", {"token": token})
                self.logger.info("Session token configured in MCP server")
            except Exception as e:
                self.logger.error(f"Failed to set session token in MCP server: {e}")
    
    async def process(
        self,
        prompt: str,
        context: Optional[Dict[str, Any]] = None,
        usage: Optional[Usage] = None,
    ) -> MinimalAgentResponse:
        """
        Process a user prompt and return a response with MCP tool access.
        """
        if not self.agent_available:
            self.logger.error("Agent not available for processing")
            return MinimalAgentResponse(
                message="I'm currently unavailable. Please try again later.",
                actions=[]
            )
        
        # Setup MCP client if not already done
        if not self.mcp_client:
            await self._setup_mcp_client()
        
        try:
            # Prepare context
            agent_context = MinimalAgentContext(context or {})
            
            # Run the agent with MCP tools available
            result = await self.agent.run(prompt, deps=agent_context, usage=usage)
            
            # Return response
            response_message = result.output if result.output else "I'm here to help! How can I assist you?"
            
            self.logger.info(f"MCP-enhanced agent processed prompt successfully: {len(response_message)} chars")
            
            return MinimalAgentResponse(
                message=response_message,
                actions=[]  # Actions could be extracted from tool calls in the future
            )
            
        except Exception as e:
            self.logger.error(f"Error processing prompt with MCP-enhanced agent: {e}")
            return MinimalAgentResponse(
                message="I encountered an error processing your request. Please try again.",
                actions=[]
            )
    
    async def cleanup(self) -> None:
        """Cleanup MCP client connection."""
        if self.mcp_client:
            try:
                await self.mcp_client.__aexit__(None, None, None)
            except Exception as e:
                self.logger.error(f"Error cleaning up MCP client: {e}")
    
    @classmethod
    def from_config(cls, config: AgentServiceConfig, mcp_server_url: str, lf: LogFactory = NO_LOG_FACTORY) -> "MCPEnhancedAgent":
        """
        Create MCPEnhancedAgent from configuration.
        """
        return cls(
            api_key=config.openai_api_key,
            mcp_server_url=mcp_server_url,
            model_name=config.agent_model_name,
            temperature=config.agent_temperature,
            timeout=config.agent_timeout,
            lf=lf
        )
