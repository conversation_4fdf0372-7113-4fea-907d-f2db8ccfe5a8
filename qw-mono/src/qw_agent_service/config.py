"""Configuration for FastAPI Agent Service."""
import os
from typing import Optional
from pydantic import BaseModel

from qw_shared_auth.models import AuthConfig


class AgentServiceConfig(BaseModel):
    """Configuration for the FastAPI agent service."""
    
    # API Keys
    openai_api_key: str
    
    # Service Configuration
    runtime_service_url: str = "http://qw-mono-dev-runtime:8000"
    
    # Authentication Configuration
    auth_config: AuthConfig
    
    # Feature Flags
    use_fastapi_agent: bool = True
    enable_specialized_agents: bool = False  # Phase 2 feature
    
    # Agent Configuration
    agent_model_name: str = "gpt-4o"
    agent_temperature: float = 0.2
    agent_timeout: float = 30.0
    
    # Performance Configuration
    max_concurrent_requests: int = 10
    request_timeout: float = 300.0  # 5 minutes
    
    @classmethod
    def from_env(cls) -> "AgentServiceConfig":
        """
        Load configuration from environment variables.
        """
        runtime_url = os.getenv("RUNTIME_SERVICE_URL", "http://qw-mono-dev-runtime:8000")
        
        auth_config = AuthConfig(
            runtime_service_url=runtime_url,
            session_validation_timeout=float(os.getenv("SESSION_VALIDATION_TIMEOUT", "10.0")),
            session_cache_ttl=int(os.getenv("SESSION_CACHE_TTL", "300"))
        )
        
        return cls(
            openai_api_key=os.getenv("OPENAI_API_KEY", ""),
            runtime_service_url=runtime_url,
            auth_config=auth_config,
            use_fastapi_agent=os.getenv("USE_FASTAPI_AGENT", "true").lower() == "true",
            enable_specialized_agents=os.getenv("ENABLE_SPECIALIZED_AGENTS", "false").lower() == "true",
            agent_model_name=os.getenv("AGENT_MODEL_NAME", "gpt-4o"),
            agent_temperature=float(os.getenv("AGENT_TEMPERATURE", "0.2")),
            agent_timeout=float(os.getenv("AGENT_TIMEOUT", "30.0")),
            max_concurrent_requests=int(os.getenv("MAX_CONCURRENT_REQUESTS", "10")),
            request_timeout=float(os.getenv("REQUEST_TIMEOUT", "300.0"))
        )
