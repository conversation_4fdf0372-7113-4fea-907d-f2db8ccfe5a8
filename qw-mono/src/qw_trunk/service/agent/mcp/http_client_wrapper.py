from typing import Any, Dict, Optional
import httpx
from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_trunk.service.agent.mcp.auth_provider import InternalAuthProvider


class MCPError(Exception):
    """Custom MCP error for better agent understanding."""
    pass


class InternalAPIClient:
    """Wrapper for making authenticated calls to our internal APIs via MCP."""

    def __init__(self, base_url: str, auth_provider: InternalAuthProvider, lf: LogFactory = NO_LOG_FACTORY):
        self.base_url = base_url.rstrip("/")
        self.auth_provider = auth_provider
        self.logger = lf.get_logger(__name__)
        self.client = httpx.AsyncClient(
    timeout=httpx.Timeout(10, connect=3),
    limits=httpx.Limits(max_keepalive_connections=20, max_connections=100),
)

    async def call_endpoint(
        self,
        method: str,
        path: str,
        params: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """Make authenticated call to internal API endpoint.

        Args:
            method: HTTP method (GET, POST, etc.)
            path: API endpoint path (should start with /)
            params: URL query parameters
            json_data: JSON request body
            **kwargs: Additional httpx request parameters

        Returns:
            JSON response as dictionary

        Raises:
            MCPError: For HTTP errors or API failures
        """
        try:
            headers = await self.auth_provider.get_headers()
            url = f"{self.base_url}{path}"

            self.logger.info(f"Making {method} request to {url}")

            response = await self.client.request(
                method=method.upper(),
                url=url,
                headers=headers,
                params=params,
                json=json_data,
                **kwargs
            )

            self._handle_http_error(response)

            # Handle empty responses
            if not response.content:
                return {}

            return response.json()

        except httpx.HTTPError as e:
            self.logger.error(f"HTTP error calling {method} {path}: {e}")
            raise MCPError(f"HTTP error: {e}")
        except Exception as e:
            self.logger.error(f"Unexpected error calling {method} {path}: {e}")
            raise MCPError(f"API call failed: {e}")

    def _handle_http_error(self, response: httpx.Response) -> None:
        """Convert HTTP errors to MCP-friendly errors.

        Args:
            response: HTTP response object

        Raises:
            MCPError: For HTTP error status codes
        """
        if response.status_code == 404:
            raise MCPError("Resource not found")
        elif response.status_code == 403:
            raise MCPError("Access denied")
        elif response.status_code == 401:
            raise MCPError("Authentication failed")
        elif response.status_code == 400:
            raise MCPError("Bad request")
        elif response.status_code >= 500:
            raise MCPError("Internal server error")
        elif response.status_code >= 400:
            raise MCPError(f"API error: {response.status_code}")

        # Raise for any other HTTP errors
        response.raise_for_status()

    async def close(self) -> None:
        """Close the HTTP client."""
        await self.client.aclose()

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:
        await self.close()
