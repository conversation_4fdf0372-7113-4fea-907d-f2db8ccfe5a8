import async<PERSON>
import httpx
import yaml
from typing import Any, Optional, Dict
from pathlib import Path

from fastmcp import FastMCP
from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_trunk.service.agent.mcp.models import MCPServerConfig


class MCPServerService:
    """FastMCP-based MCP Server service that exposes internal APIs as MCP tools."""

    def __init__(self, config: MCPServerConfig, lf: LogFactory = NO_LOG_FACTORY):
        self.config = config
        self.lf = lf
        self.logger = lf.get_logger(__name__)

        # Create FastMCP server instance
        self.server = FastMCP(name="QW Internal API Server")

        # Create HTTP client for internal API calls
        self.api_client = httpx.AsyncClient(
            base_url=config.api_base_url,
            timeout=30.0
        )

        # Session token for authentication
        self.current_session_token: Optional[str] = None

        # Initialize tools
        self._setup_tools()

    def _setup_tools(self) -> None:
        """Setup MCP tools using FastMCP's OpenAPI integration."""
        try:
            # Load OpenAPI spec and create tools
            if self.config.enable_auto_generated_tools:
                self._setup_openapi_tools()

            # Add session management tool
            self._setup_session_tools()

            self.logger.info("MCP tools setup completed successfully")

        except Exception as e:
            self.logger.error(f"Failed to setup MCP tools: {e}")
            raise

    def set_session_token(self, session_token: str) -> None:
        """Set session token for API authentication."""
        self.current_session_token = session_token
        self.logger.info(f"Session token set for MCP server: {session_token[:10]}...")

        # Update HTTP client headers with session cookie
        self.api_client.cookies.set("session", session_token)

    def _setup_openapi_tools(self) -> None:
        """Setup tools from OpenAPI specification using FastMCP."""
        try:
            # Load OpenAPI spec
            spec_path = Path(self.config.openapi_spec_path)
            if not spec_path.is_absolute():
                # Try to find the spec relative to project root
                project_root = Path(__file__).parents[5]  # Go up to qw-mono root
                spec_path = project_root / self.config.openapi_spec_path

            if not spec_path.exists():
                raise FileNotFoundError(f"OpenAPI spec not found at {spec_path}")

            # Load the OpenAPI spec as a dictionary
            with open(spec_path, 'r') as f:
                openapi_spec: Dict[str, Any] = yaml.safe_load(f)

            # Create a new FastMCP server from OpenAPI spec
            # This replaces the manual tool creation with FastMCP's auto-generation
            openapi_server = FastMCP.from_openapi(
                openapi_spec=openapi_spec,
                client=self.api_client,
                name="QW Internal API Tools"
            )

            # For now, let's manually add some key tools instead of importing the whole server
            # This is a simpler approach that avoids complex server composition
            self._add_key_api_tools()

            self.logger.info("OpenAPI tools added to FastMCP server")

        except Exception as e:
            self.logger.error(f"Failed to setup OpenAPI tools: {e}")
            # Don't raise - continue without auto-generated tools
            self.logger.warning("Continuing without auto-generated OpenAPI tools")

    def _setup_session_tools(self) -> None:
        """Setup session management tools."""

        @self.server.tool
        def set_session_token(token: str) -> str:
            """Set the session token for API authentication."""
            self.set_session_token(token)
            return f"Session token set successfully: {token[:10]}..."

        @self.server.tool
        async def get_api_health() -> str:
            """Check the health of the internal API."""
            try:
                response = await self.api_client.get("/api/health")
                response.raise_for_status()
                data = response.json()
                return f"API Health: {data.get('status', 'unknown')}"
            except Exception as e:
                return f"Health check failed: {e}"

    def _register_basic_tools(self) -> None:
        """Register basic MCP tools for common API operations."""
        self.logger.info("[MCP_BASIC_TOOLS] Starting basic tool registration")

        @self.server.tool()
        async def set_session_token(token: str) -> str:
            """Internal tool to set session token for authentication."""
            self.logger.info(f"[MCP_SERVER_SET_TOKEN_CALLED] set_session_token tool called with token: {token[:10]}...")
            self.set_session_token(token)
            self.logger.info(f"[MCP_SERVER_SET_TOKEN_COMPLETE] Session token set successfully")
            return f"Session token set: {token[:10]}..."

        @self.server.tool()
        async def get_health() -> str:
            """Check the health status of the internal API server."""
            try:
                result = await self.api_client.call_endpoint("GET", "/api/health")
                return f"API Health: {result.get('status', 'unknown')}"
            except MCPError as e:
                return f"Health check failed: {e}"

        @self.server.tool()
        async def get_api_info() -> str:
            """Get information about the internal API server."""
            try:
                result = await self.api_client.call_endpoint("GET", "/api/info")
                version = result.get('version', 'unknown')
                commit = result.get('commit', 'unknown')
                return f"API Info - Version: {version}, Commit: {commit[:8]}"
            except MCPError as e:
                return f"Info request failed: {e}"

        try:
            self.logger.info("[MCP_BASIC_TOOLS] Registering async_sleep_test tool")
            @self.server.tool()
            async def async_sleep_test(seconds: float = 0.1) -> str:
                """Test async behavior with controlled delay."""
                start = time.time()
                self.logger.info(f"[MCP_ASYNC_TEST] Starting async sleep for {seconds}s at {start}")
                await asyncio.sleep(seconds)
                end = time.time()
                actual_duration = end - start
                self.logger.info(f"[MCP_ASYNC_TEST] Completed after {actual_duration:.3f}s")
                return f"Requested {seconds}s, actual {actual_duration:.3f}s"
            self.logger.info("[MCP_BASIC_TOOLS] async_sleep_test tool registered successfully")
        except Exception as e:
            self.logger.error(f"[MCP_BASIC_TOOLS] Failed to register async_sleep_test tool: {e}")

        try:
            self.logger.info("[MCP_BASIC_TOOLS] Registering simple_http_call tool")
            @self.server.tool()
            async def simple_http_call() -> str:
                """HTTP call without authentication or complex headers."""
                import httpx
                start = time.time()
                self.logger.info(f"[MCP_HTTP_TEST] Starting external HTTP call at {start}")
                try:
                    async with httpx.AsyncClient(timeout=10.0) as client:
                        response = await client.get('http://httpbin.org/json')
                        end = time.time()
                        duration = end - start
                        self.logger.info(f"[MCP_HTTP_TEST] External HTTP completed in {duration:.3f}s, status: {response.status_code}")
                        return f"External HTTP call took {duration:.3f}s, status: {response.status_code}"
                except Exception as e:
                    end = time.time()
                    duration = end - start
                    self.logger.error(f"[MCP_HTTP_TEST] External HTTP failed after {duration:.3f}s: {e}")
                    return f"External HTTP failed after {duration:.3f}s: {e}"
            self.logger.info("[MCP_BASIC_TOOLS] simple_http_call tool registered successfully")
        except Exception as e:
            self.logger.error(f"[MCP_BASIC_TOOLS] Failed to register simple_http_call tool: {e}")

        try:
            self.logger.info("[MCP_BASIC_TOOLS] Registering list_materials tool")
            @self.server.tool()
            async def list_materials(tenant_id: int, limit: int = 10) -> str:
                """List materials for a specific tenant.

                Args:
                    tenant_id: The tenant ID to list materials for
                    limit: Maximum number of materials to return (default: 10)
                """
                try:
                    params = {"limit": str(limit)}
                    result = await self.api_client.call_endpoint(
                        "GET",
                        f"/api/v1/tenant/{tenant_id}/material",
                        params=params
                    )
                    materials = result.get('materials', [])
                    if not materials:
                        return "No materials found for this tenant."

                    material_list = []
                    for material in materials[:limit]:
                        name = material.get('name', 'Unknown')
                        material_id = material.get('id', 'Unknown')
                        material_list.append(f"- {name} (ID: {material_id})")

                    return f"Materials for tenant {tenant_id}:\n" + "\n".join(material_list)  # type: ignore[misc]
                except MCPError as e:
                    return f"Failed to list materials: {e}"
            self.logger.info("[MCP_BASIC_TOOLS] list_materials tool registered successfully")
        except Exception as e:
            self.logger.error(f"[MCP_BASIC_TOOLS] Failed to register list_materials tool: {e}")

        try:
            self.logger.info("[MCP_BASIC_TOOLS] Registering search_materials tool")
            @self.server.tool()
            async def search_materials(tenant_id: int, query: str, limit: int = 5) -> str:
                """Search for materials by name or description.

                Args:
                    tenant_id: The tenant ID to search within
                    query: Search query string
                    limit: Maximum number of results to return (default: 5)
                """
                try:
                    json_data: dict[str, Any] = {  # type: ignore[misc]
                        "query": query,
                        "limit": limit
                    }
                    result = await self.api_client.call_endpoint(
                        "POST",
                        f"/api/v1/tenant/{tenant_id}/material/search",
                        json_data=json_data
                    )
                    materials = result.get('materials', [])
                    if not materials:
                        return f"No materials found matching '{query}'."

                    material_list = []
                    for material in materials:
                        name = material.get('name', 'Unknown')
                        material_id = material.get('id', 'Unknown')
                        description = material.get('description', '')
                        desc_text = f" - {description}" if description else ""
                        material_list.append(f"- {name} (ID: {material_id}){desc_text}")

                    return f"Materials matching '{query}':\n" + "\n".join(material_list)  # type: ignore[misc]
                except MCPError as e:
                    return f"Material search failed: {e}"
            self.logger.info("[MCP_BASIC_TOOLS] search_materials tool registered successfully")
        except Exception as e:
            self.logger.error(f"[MCP_BASIC_TOOLS] Failed to register search_materials tool: {e}")

        self.logger.info("[MCP_BASIC_TOOLS] Basic tool registration completed")

    def _register_auto_generated_tools(self) -> None:
        """Register auto-generated tools from OpenAPI specification."""
        try:
            self.logger.info("Registering auto-generated MCP tools from OpenAPI spec")

            generator = OpenAPIToMCPGenerator(
                self.config.openapi_spec_path,
                self.api_client,
                self.lf
            )

            # Generate tools with some filtering to avoid overwhelming the agent
            include_patterns = [
                r'/api/v1/material.*',      # Material operations
                r'/api/v1/file-resource.*', # File operations
                r'/api/v1/inspection.*',    # Inspection operations
                r'/api/v1/order.*',         # Order operations
                r'/api/v1/tenant.*',        # Tenant operations
                r'/api/v1/user.*',          # User operations
            ]

            tools = generator.generate_mcp_tools(include_patterns=include_patterns)

            # Register each tool with the server
            for tool in tools:
                self.server.tool()(tool)

            self.logger.info(f"Successfully registered {len(tools)} auto-generated MCP tools")

        except Exception as e:
            self.logger.error(f"Failed to register auto-generated tools: {e}")
            # Don't raise - continue with other tools

    def _register_manual_tools(self) -> None:
        """Register manually implemented priority tools."""
        try:
            self.logger.info("[MCP_MANUAL_TOOLS] Starting manual tool registration")

            manual_tools = ManualMCPTools(self.api_client, self.lf)
            tools = manual_tools.get_tools()

            # Debug: Log each tool being registered
            self.logger.info(f"[MCP_MANUAL_TOOLS] Found {len(tools)} manual tools to register")

            # Register each tool with the server with individual error handling
            registered_count = 0
            for i, tool in enumerate(tools):
                tool_name = getattr(tool, '__name__', f'tool_{i}')
                try:
                    self.logger.info(f"[MCP_MANUAL_TOOLS] Registering tool: {tool_name}")
                    self.server.tool()(tool)
                    registered_count += 1
                    self.logger.info(f"[MCP_MANUAL_TOOLS] Successfully registered tool: {tool_name}")
                except Exception as e:
                    self.logger.error(f"[MCP_MANUAL_TOOLS] Failed to register tool {tool_name}: {e}")
                    # Continue with other tools

            self.logger.info(f"[MCP_MANUAL_TOOLS] Manual tool registration completed: {registered_count}/{len(tools)} tools registered successfully")

        except Exception as e:
            self.logger.error(f"[MCP_MANUAL_TOOLS] Failed to register manual tools: {e}")
            import traceback
            self.logger.error(f"[MCP_MANUAL_TOOLS] Full traceback: {traceback.format_exc()}")
            # Don't raise - continue with other tools

    async def start_server(self) -> None:
        """Start the MCP server asynchronously."""
        if not self.config.enabled:
            self.logger.info("MCP server is disabled in configuration")
            return

        if self._server_task is not None:
            self.logger.warning("MCP server is already running")
            return

        self.logger.info(f"Starting MCP server with transport {self.config.transport} on port {self.config.port}")

        # Add runtime diagnostics
        self._log_runtime_binding_info()

        try:
            # FastMCP server.run() is blocking, so we run it in a thread
            def run_server():
                # FastMCP handles host/port configuration internally
                self.server.run(transport=self.config.transport)

            # Run the blocking server in a thread pool
            loop = asyncio.get_event_loop()
            self._server_task = loop.run_in_executor(None, run_server)
            self.logger.info(f"MCP server started successfully with transport {self.config.transport} on {self.config.host}:{self.config.port}")
        except Exception as e:
            self.logger.error(f"Failed to start MCP server: {e}")
            raise

    async def stop_server(self) -> None:
        """Stop the MCP server."""
        if self._server_task is None:
            return

        self.logger.info("Stopping MCP server")
        self._server_task.cancel()

        try:
            await self._server_task
        except asyncio.CancelledError:
            pass

        self._server_task = None
        await self.api_client.close()
        self.logger.info("MCP server stopped")

    def is_running(self) -> bool:
        """Check if the MCP server is running."""
        return self._server_task is not None and not self._server_task.done()

    def start_server_sync(self) -> None:
        """Start the MCP server synchronously using a background thread."""
        if not self.config.enabled:
            self.logger.info("MCP server is disabled in configuration")
            return

        if self._server_task is not None:
            self.logger.warning("MCP server is already running")
            return

        self.logger.info(f"Starting MCP server with transport {self.config.transport} on port {self.config.port}")

        # Add runtime diagnostics
        self._log_runtime_binding_info()

        try:
            # Use a daemon thread to run the server
            def run_server():
                try:
                    self.server.run(transport=self.config.transport)
                except Exception as e:
                    self.logger.error(f"MCP server thread failed: {e}")

            # Start server in a daemon thread
            server_thread = threading.Thread(target=run_server, daemon=True)
            server_thread.start()

            # Store the thread as a future-like object for state tracking
            # Create a simple future-like object to track the thread
            future: asyncio.Future[Any] = asyncio.Future()

            def monitor_thread():
                server_thread.join()
                if not future.cancelled():
                    future.set_result(None)

            monitor_thread_obj = threading.Thread(target=monitor_thread, daemon=True)
            monitor_thread_obj.start()
            self._server_task = future

            # Give the server a moment to start
            time.sleep(1)

            self.logger.info(f"MCP server started successfully with transport {self.config.transport} on {self.config.host}:{self.config.port}")
        except Exception as e:
            self.logger.error(f"Failed to start MCP server: {e}")
            # Don't raise - allow the system to continue without MCP
            self.logger.warning("System will continue without MCP server")

    def run_standalone(self) -> None:
        """Run the MCP server as a standalone application (for separate container)."""
        if not self.config.enabled:
            self.logger.error("MCP server is disabled in configuration")
            return

        # Debug point 2: Log configuration before running and check FastMCP internal settings
        self.logger.info(f"Starting MCP server with config: host={self.config.host}, port={self.config.port}, transport={self.config.transport}")
        if hasattr(self.server, 'settings'):
            actual_settings = self.server.settings
            self.logger.info(f"FastMCP internal settings before run(): host={getattr(actual_settings, 'host', 'unknown')}, port={getattr(actual_settings, 'port', 'unknown')}")

        try:
            # Add runtime port detection - try to determine what port FastMCP will actually bind to
            self._log_runtime_binding_info()

            # Run with the configured transport
            try:
                self.logger.info(f"Starting MCP server with transport: {self.config.transport}")
                self.server.run(transport=self.config.transport)
            except Exception as e:
                self.logger.error(f"Failed to run MCP server with transport {self.config.transport}: {e}")
                raise
        except Exception as e:
            self.logger.error(f"Failed to run MCP server standalone: {e}")
            raise

    def _log_runtime_binding_info(self) -> None:
        """Log information about what port/host the server will bind to."""
        import os
        import socket

        # Check environment variables that FastMCP might use
        env_host = os.environ.get('MCP_SERVER_HOST', os.environ.get('HOST', 'not_set'))
        env_port = os.environ.get('MCP_SERVER_PORT', os.environ.get('PORT', 'not_set'))

        self.logger.info(f"Environment variables: MCP_SERVER_HOST={env_host}, MCP_SERVER_PORT={env_port}")

        # Check if port 8000 is available (FastMCP default)
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('0.0.0.0', 8000))
                self.logger.info("Port 8000 is available for binding")
        except OSError as e:
            self.logger.warning(f"Port 8000 is not available: {e}")

        # Check if configured port is available
        if self.config.port != 8000:
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('0.0.0.0', self.config.port))
                    self.logger.info(f"Configured port {self.config.port} is available for binding")
            except OSError as e:
                self.logger.warning(f"Configured port {self.config.port} is not available: {e}")

        # Log what we expect vs what might happen
        self.logger.info(f"Expected binding: {self.config.host}:{self.config.port}")
        self.logger.info(f"Note: FastMCP may use different host/port if configured differently in constructor")

    @classmethod
    def from_config(cls, config: MCPServerConfig, lf: LogFactory = NO_LOG_FACTORY) -> "MCPServerService":
        """Create MCP server service from configuration."""
        return cls(config, lf)
