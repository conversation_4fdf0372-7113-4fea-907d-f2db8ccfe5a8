#!/usr/bin/env python3
"""
MCP Server Standalone Entrypoint

This script runs the MCP server as a standalone application in a separate container.
It loads the configuration and starts the MCP server to expose internal APIs as MCP tools.
"""

import argparse
import os
import sys
from pathlib import Path

from qw_config.loader import load_config
from qw_log.factory import QwLogFactory
from qw_mono.app import QwAppInfo
from qw_mono.config import QwMonoConfig
from qw_trunk.service.agent.mcp.fastmcp_server import FastMCPServerService


def main() -> None:
    """Main entrypoint for the MCP server."""
    parser = argparse.ArgumentParser("mcp-server")
    parser.add_argument("--qw-mono-config", metavar="FILE", type=Path, required=True)
    parser.add_argument("--qw-mono-overwrite-config", metavar="FILE", type=Path, default=None)

    args = parser.parse_args()

    try:
        # Load configuration
        config = load_config(QwMonoConfig, args.qw_mono_config, args.qw_mono_overwrite_config)

        # Get version and commit from environment (same pattern as main app)
        version = os.environ.get("BUILD_VERSION", "0.0.0.0")
        commit = os.environ.get("BUILD_COMMIT", "0" * 40)

        print(f"DEBUG: Using version='{version}', commit='{commit}'")

        # Initialize logging (same pattern as main app in qw_mono/server.py)
        app_info = QwAppInfo(
            name="qw-mcp-server",
            version=version,
            commit=commit,
        )
        log_factory = QwLogFactory(app_info.version)
        log_factory.init_logs(config.logging)
        logger = log_factory.get_logger(__name__)

        logger.info("Starting MCP server standalone")

        # Check if MCP is enabled
        mcp_settings = config.mono_trunk.mcp_settings
        if not mcp_settings or not mcp_settings.server.enabled:
            logger.error("MCP server is not enabled in configuration")
            sys.exit(1)

        # Create and run FastMCP server (host and port configured via dependency injection)
        mcp_server = FastMCPServerService(mcp_settings.server, log_factory)
        logger.info(f"FastMCP server configured for {mcp_settings.server.host}:{mcp_settings.server.port}")

        # Run the server (this will block)
        mcp_server.run_server()

    except KeyboardInterrupt:
        print("\nMCP server stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"Failed to start MCP server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
