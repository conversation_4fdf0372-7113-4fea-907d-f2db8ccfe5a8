"""Clean FastMCP-based MCP Server implementation."""
import async<PERSON>
import httpx
import yaml
from typing import Any, Optional, Dict
from pathlib import Path

from fastmcp import FastMCP
from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_trunk.service.agent.mcp.models import MCPServerConfig


class FastMCPServerService:
    """Clean FastMCP-based MCP Server service that exposes internal APIs as MCP tools."""

    def __init__(self, config: MCPServerConfig, lf: LogFactory = NO_LOG_FACTORY):
        self.config = config
        self.lf = lf
        self.logger = lf.get_logger(__name__)

        # FastMCP server instance (will be created from OpenAPI spec or basic server)
        self.server = FastMCP(name="QW Internal API Server")

        # Create HTTP client for internal API calls
        self.api_client = httpx.AsyncClient(
            base_url=config.api_base_url,
            timeout=30.0
        )

        # Session token for authentication
        self.current_session_token: Optional[str] = None

        # Initialize tools
        self._setup_tools()

    def _setup_tools(self) -> None:
        """Setup MCP tools."""
        try:
            # Only add OpenAPI auto-generated tools
            if self.config.enable_auto_generated_tools:
                self._setup_openapi_tools()

            self.logger.info("MCP tools setup completed successfully")

        except Exception as e:
            self.logger.error(f"Failed to setup MCP tools: {e}")
            raise

    def set_session_token(self, session_token: str) -> None:
        """Set session token for API authentication."""
        self.current_session_token = session_token
        self.logger.info(f"Session token set for MCP server: {session_token[:10]}...")

        # Update HTTP client headers with session cookie
        self.api_client.cookies.set("session", session_token)

    def _setup_openapi_tools(self) -> None:
        """Setup tools from OpenAPI specification using FastMCP."""
        try:
            # Load OpenAPI spec
            spec_path = Path(self.config.openapi_spec_path)
            if not spec_path.is_absolute():
                # Try to find the spec relative to project root
                project_root = Path(__file__).parents[5]  # Go up to qw-mono root
                spec_path = project_root / self.config.openapi_spec_path

            if not spec_path.exists():
                self.logger.warning(f"OpenAPI spec not found at {spec_path}, skipping auto-generated tools")
                return

            # Load the OpenAPI spec as a dictionary
            with open(spec_path, 'r') as f:
                openapi_spec: Dict[str, Any] = yaml.safe_load(f)

            # Use FastMCP's OpenAPI integration to auto-generate all tools
            # Load the spec as a dictionary for FastMCP
            with open(spec_path, 'r') as f:
                openapi_spec = yaml.safe_load(f)

            # Replace the basic server with OpenAPI-generated server
            self.server = FastMCP.from_openapi(
                openapi_spec=openapi_spec,
                client=self.api_client,
                name="QW Internal API Server"
            )

            self.logger.info("OpenAPI tools auto-generated and added to FastMCP server")

        except Exception as e:
            self.logger.error(f"Failed to setup OpenAPI tools: {e}")
            # Keep the basic server that was already created
            self.logger.warning("Continuing with basic MCP server without auto-generated tools")

    def run_server(self) -> None:
        """Run the MCP server."""
        if not self.config.enabled:
            self.logger.error("MCP server is disabled in configuration")
            return

        if not self.server:
            self.logger.error("MCP server not initialized")
            return

        self.logger.info(f"Starting FastMCP server on {self.config.host}:{self.config.port}")

        try:
            # Run the FastMCP server with the configured transport
            self.server.run(
                transport=self.config.transport,
                host=self.config.host,
                port=self.config.port
            )
        except Exception as e:
            self.logger.error(f"Failed to run FastMCP server: {e}")
            raise

    async def cleanup(self) -> None:
        """Cleanup resources."""
        await self.api_client.aclose()

    @classmethod
    def from_config(cls, config: MCPServerConfig, lf: LogFactory = NO_LOG_FACTORY) -> "FastMCPServerService":
        """Create FastMCP server service from configuration."""
        return cls(config, lf)
