"""Clean FastMCP-based MCP Server implementation."""
import async<PERSON>
import httpx
import yaml
from typing import Any, Optional, Dict
from pathlib import Path

from fastmcp import FastMCP
from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_trunk.service.agent.mcp.models import MCPServerConfig


class FastMCPServerService:
    """Clean FastMCP-based MCP Server service that exposes internal APIs as MCP tools."""

    def __init__(self, config: MCPServerConfig, lf: LogFactory = NO_LOG_FACTORY):
        self.config = config
        self.lf = lf
        self.logger = lf.get_logger(__name__)
        
        # Create FastMCP server instance
        self.server = FastMCP(name="QW Internal API Server")
        
        # Create HTTP client for internal API calls
        self.api_client = httpx.AsyncClient(
            base_url=config.api_base_url,
            timeout=30.0
        )
        
        # Session token for authentication
        self.current_session_token: Optional[str] = None
        
        # Initialize tools
        self._setup_tools()

    def _setup_tools(self) -> None:
        """Setup MCP tools."""
        try:
            # Add session management tool
            self._setup_session_tools()
            
            # Add basic API tools
            self._setup_basic_api_tools()
            
            # Add OpenAPI tools if enabled
            if self.config.enable_auto_generated_tools:
                self._setup_openapi_tools()
            
            self.logger.info("MCP tools setup completed successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to setup MCP tools: {e}")
            raise

    def set_session_token(self, session_token: str) -> None:
        """Set session token for API authentication."""
        self.current_session_token = session_token
        self.logger.info(f"Session token set for MCP server: {session_token[:10]}...")
        
        # Update HTTP client headers with session cookie
        self.api_client.cookies.set("session", session_token)

    def _setup_session_tools(self) -> None:
        """Setup session management tools."""
        
        @self.server.tool
        def set_session_token(token: str) -> str:
            """Set the session token for API authentication."""
            self.set_session_token(token)
            return f"Session token set successfully: {token[:10]}..."

    def _setup_basic_api_tools(self) -> None:
        """Setup basic API tools."""
        
        @self.server.tool
        async def get_api_health() -> str:
            """Check the health of the internal API."""
            try:
                response = await self.api_client.get("/api/health")
                response.raise_for_status()
                data = response.json()
                return f"API Health: {data.get('status', 'unknown')}"
            except Exception as e:
                return f"Health check failed: {e}"
        
        @self.server.tool
        async def list_materials(tenant_id: int, limit: int = 10) -> str:
            """List materials for a specific tenant."""
            try:
                response = await self.api_client.get(
                    f"/api/v1/tenant/{tenant_id}/material",
                    params={"limit": limit}
                )
                response.raise_for_status()
                data = response.json()
                
                materials = data.get('materials', [])
                if not materials:
                    return "No materials found for this tenant."
                
                material_list = []
                for material in materials[:limit]:
                    name = material.get('name', 'Unknown')
                    material_id = material.get('id', 'Unknown')
                    material_list.append(f"- {name} (ID: {material_id})")
                
                return f"Materials for tenant {tenant_id}:\n" + "\n".join(material_list)
                
            except Exception as e:
                return f"Failed to list materials: {e}"

    def _setup_openapi_tools(self) -> None:
        """Setup tools from OpenAPI specification using FastMCP."""
        try:
            # Load OpenAPI spec
            spec_path = Path(self.config.openapi_spec_path)
            if not spec_path.is_absolute():
                # Try to find the spec relative to project root
                project_root = Path(__file__).parents[5]  # Go up to qw-mono root
                spec_path = project_root / self.config.openapi_spec_path
            
            if not spec_path.exists():
                self.logger.warning(f"OpenAPI spec not found at {spec_path}, skipping auto-generated tools")
                return
            
            # Load the OpenAPI spec as a dictionary
            with open(spec_path, 'r') as f:
                openapi_spec: Dict[str, Any] = yaml.safe_load(f)
            
            # Use FastMCP's OpenAPI integration to create a separate server
            # Then manually extract and register key tools
            self._register_key_openapi_tools(openapi_spec)
            
            self.logger.info("Key OpenAPI tools added to FastMCP server")
            
        except Exception as e:
            self.logger.error(f"Failed to setup OpenAPI tools: {e}")
            # Don't raise - continue without auto-generated tools
            self.logger.warning("Continuing without auto-generated OpenAPI tools")

    def _register_key_openapi_tools(self, openapi_spec: Dict[str, Any]) -> None:
        """Register key tools from OpenAPI spec manually."""
        # For now, we'll add a few key tools manually
        # This is simpler than trying to auto-generate everything
        
        @self.server.tool
        async def search_materials(tenant_id: int, query: str, limit: int = 5) -> str:
            """Search for materials by name or description."""
            try:
                response = await self.api_client.post(
                    f"/api/v1/tenant/{tenant_id}/material/search",
                    json={"query": query, "limit": limit}
                )
                response.raise_for_status()
                data = response.json()
                
                materials = data.get('materials', [])
                if not materials:
                    return f"No materials found matching '{query}'."
                
                material_list = []
                for material in materials:
                    name = material.get('name', 'Unknown')
                    material_id = material.get('id', 'Unknown')
                    description = material.get('description', '')
                    desc_text = f" - {description}" if description else ""
                    material_list.append(f"- {name} (ID: {material_id}){desc_text}")
                
                return f"Materials matching '{query}':\n" + "\n".join(material_list)
                
            except Exception as e:
                return f"Material search failed: {e}"

    def run_server(self) -> None:
        """Run the MCP server."""
        if not self.config.enabled:
            self.logger.error("MCP server is disabled in configuration")
            return
        
        self.logger.info(f"Starting FastMCP server on {self.config.host}:{self.config.port}")
        
        try:
            # Run the FastMCP server
            self.server.run(
                transport=self.config.transport,
                host=self.config.host,
                port=self.config.port
            )
        except Exception as e:
            self.logger.error(f"Failed to run FastMCP server: {e}")
            raise

    async def cleanup(self) -> None:
        """Cleanup resources."""
        await self.api_client.aclose()

    @classmethod
    def from_config(cls, config: MCPServerConfig, lf: LogFactory = NO_LOG_FACTORY) -> "FastMCPServerService":
        """Create FastMCP server service from configuration."""
        return cls(config, lf)
