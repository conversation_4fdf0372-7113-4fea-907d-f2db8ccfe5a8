import re
from pathlib import Path
from typing import Any, <PERSON>wai<PERSON>, Callable, Dict, List, Optional, <PERSON><PERSON>
import yaml
from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_trunk.service.agent.mcp.http_client_wrapper import InternalAPIClient, MCPError


class OpenAPIToMCPGenerator:
    """Generator that creates MCP tools from OpenAPI specification."""

    def __init__(self, openapi_spec_path: str, api_client: InternalAPIClient, lf: LogFactory = NO_LOG_FACTORY):
        self.spec_path = openapi_spec_path
        self.api_client = api_client
        self.logger = lf.get_logger(__name__)
        self.spec_data: Optional[Dict[str, Any]] = None

    def _load_spec(self) -> Dict[str, Any]:
        """Load and parse OpenAPI YAML specification."""
        if self.spec_data is not None:
            return self.spec_data

        try:
            spec_path = Path(self.spec_path)

            # If not absolute, try different relative paths
            if not spec_path.is_absolute():
                search_paths = [
                    Path.cwd() / self.spec_path,  # Current working directory
                    Path(__file__).parents[6] / self.spec_path,  # qw-mono directory (project root)
                    Path(__file__).parents[5] / self.spec_path,  # src directory
                    Path(__file__).parents[4] / self.spec_path,  # qw_trunk directory
                ]

                # Log search paths for debugging
                self.logger.info(f"Searching for OpenAPI spec '{self.spec_path}' in {len(search_paths)} paths")

                for candidate_path in search_paths:
                    if candidate_path.exists():
                        spec_path = candidate_path
                        self.logger.info(f"Found OpenAPI spec at: {spec_path}")
                        break
                else:
                    # If none found, raise error with all attempted paths
                    attempted_paths = [str(p) for p in search_paths]
                    self.logger.error(f"OpenAPI spec not found. Current working directory: {Path.cwd()}")
                    self.logger.error(f"Generator file location: {Path(__file__)}")
                    raise FileNotFoundError(f"OpenAPI spec not found at {self.spec_path}. Tried: {', '.join(attempted_paths)}")

            elif not spec_path.exists():
                raise FileNotFoundError(f"OpenAPI spec not found at absolute path: {spec_path}")

            with spec_path.open('r') as f:
                self.spec_data = yaml.safe_load(f)

            if self.spec_data is None:
                raise ValueError("OpenAPI spec file is empty or invalid")

            self.logger.info(f"Loaded OpenAPI spec from {spec_path}")
            return self.spec_data

        except Exception as e:
            self.logger.error(f"Failed to load OpenAPI spec: {e}")
            raise

    def generate_mcp_tools(self, include_patterns: Optional[List[str]] = None, exclude_patterns: Optional[List[str]] = None) -> List[Callable[..., Awaitable[str]]]:
        """Generate MCP tools for OpenAPI endpoints.

        Args:
            include_patterns: List of regex patterns for paths to include
            exclude_patterns: List of regex patterns for paths to exclude
        """
        spec = self._load_spec()
        tools = []
        paths = spec.get('paths', {})

        # Default exclusions for auth and internal endpoints
        default_excludes = [
            r'/api/v1/session/.*',  # Authentication endpoints
            r'/api/openapi.*',      # OpenAPI spec endpoints
            r'/api/health',         # Health check
            r'/api/info',           # Info endpoint
        ]

        exclude_patterns = (exclude_patterns or []) + default_excludes

        for path, path_item in paths.items():
            # Check include/exclude patterns
            if include_patterns and not any(re.search(pattern, path) for pattern in include_patterns):
                continue

            if any(re.search(pattern, path) for pattern in exclude_patterns):
                continue

            for method, operation in path_item.items():
                if method.lower() in ['get', 'post', 'put', 'delete', 'patch'] and isinstance(operation, dict):
                    try:
                        tool = self._create_tool_from_operation(path, method.lower(), operation)  # type: ignore[misc]
                        if tool:
                            tools.append(tool)
                    except Exception as e:
                        self.logger.warning(f"Failed to create tool for {method.upper()} {path}: {e}")

        self.logger.info(f"Generated {len(tools)} MCP tools from OpenAPI spec")  # type: ignore[misc]
        return tools  # type: ignore[misc]

    def _create_tool_from_operation(self, path: str, method: str, operation: Dict[str, Any]) -> Optional[Callable[..., Awaitable[str]]]:
        """Create an MCP tool function from OpenAPI operation with explicit parameter signature."""
        operation_id = operation.get('operationId')
        if not operation_id:
            # Generate operation ID from path and method
            clean_path = re.sub(r'[{}]', '', path).replace('/', '_').strip('_')
            operation_id = f"{method}_{clean_path}"

        # Convert to snake_case for Python function names
        function_name = self._to_snake_case(operation_id)

        description = self._build_description(operation, path, method)
        parameters = self._extract_parameters(operation, path)

        # Build function signature dynamically
        return self._create_dynamic_function(function_name, description, path, method, operation_id, parameters)

    def _create_dynamic_function(self, function_name: str, description: str, path: str, method: str, operation_id: str, parameters: Dict[str, Any]) -> Callable[..., Awaitable[str]]:
        """Create a function with explicit parameter signature based on OpenAPI parameters."""
        # Build parameter list for function signature
        param_definitions = []
        param_names = []

        # Add path parameters
        path_params = parameters.get('path', [])
        if isinstance(path_params, list):
            for param in path_params:
                if isinstance(param, dict):
                    param_name = param.get('name')
                    param_type = param.get('type', 'string')
                    if param_name:
                        # Convert OpenAPI types to Python types
                        python_type = self._openapi_type_to_python(param_type)
                        param_definitions.append(f"{param_name}: {python_type}")
                        param_names.append(param_name)

        # Add query parameters
        query_params = parameters.get('query', [])
        if isinstance(query_params, list):
            for param in query_params:
                if isinstance(param, dict):
                    param_name = param.get('name')
                    param_type = param.get('type', 'string')
                    required = param.get('required', False)
                    if param_name:
                        python_type = self._openapi_type_to_python(param_type)
                        if not required:
                            param_definitions.append(f"{param_name}: Optional[{python_type}] = None")
                        else:
                            param_definitions.append(f"{param_name}: {python_type}")
                        param_names.append(param_name)

        # Add body parameters (for POST/PUT requests)
        body_config = parameters.get('body')
        if body_config:
            param_definitions.append("request_body: Optional[Dict[str, Any]] = None")
            param_names.append('request_body')

        # Build function signature string
        params_str = ", ".join(param_definitions)

        # Create function code dynamically
        kwargs_assignment = self._build_kwargs_assignment(param_names)

        function_code = f'''async def {function_name}({params_str}) -> str:
    """
    {description}
    """
    try:
        # Collect all parameters into kwargs for compatibility with existing logic
        kwargs = {{}}
{kwargs_assignment}

        # Map parameters to API call components
        path_with_params, query_params, json_data = self._map_parameters_from_explicit_args(
            "{path}", parameters, kwargs
        )

        # Make API call using the global API client (which uses current session token)
        result = await self.api_client.call_endpoint(
            "{method.upper()}",
            path_with_params,
            params=query_params,
            json_data=json_data
        )

        # Format response for agent
        return self._format_response(result, "{operation_id}")

    except Exception as e:
        self.logger.error(f"Error in {operation_id}: {{e}}")
        return f"Error calling {operation_id}: {{e}}"
'''

        # Create the function using exec
        namespace = {
            'self': self,
            'parameters': parameters,
            'Optional': Optional,
            'Dict': Dict,
            'Any': Any,
        }

        exec(function_code, namespace)
        generated_function = namespace[function_name]

        return generated_function

    def _openapi_type_to_python(self, openapi_type: str) -> str:
        """Convert OpenAPI type to Python type string."""
        type_mapping = {
            'string': 'str',
            'integer': 'int',
            'number': 'float',
            'boolean': 'bool',
            'array': 'List[Any]',
            'object': 'Dict[str, Any]'
        }
        return type_mapping.get(openapi_type, 'str')

    def _build_kwargs_assignment(self, param_names: List[str]) -> str:
        """Build kwargs assignment code for parameter collection."""
        if not param_names:
            return "        # No parameters to assign"

        assignments = []
        for param_name in param_names:
            assignments.append(f"        if {param_name} is not None:")
            assignments.append(f"            kwargs['{param_name}'] = {param_name}")
        return "\n".join(assignments)

    def _map_parameters_from_explicit_args(self, path: str, parameters: Dict[str, Any], kwargs: Dict[str, Any]) -> Tuple[str, Dict[str, Any], Optional[Dict[str, Any]]]:
        """Map explicit function arguments to API call components (reuses existing logic)."""
        return self._map_parameters(path, parameters, kwargs)

    def _to_snake_case(self, name: str) -> str:
        """Convert CamelCase to snake_case."""
        # Insert underscore before uppercase letters
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
        # Insert underscore before uppercase letters preceded by lowercase
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()

    def _build_description(self, operation: Dict[str, Any], path: str, method: str) -> str:
        """Build comprehensive description for the tool."""
        summary = operation.get('summary', '')
        description = operation.get('description', '')

        if summary and description:
            desc = f"{summary}\n\n{description}"
        elif summary:
            desc = summary
        elif description:
            desc = description
        else:
            desc = f"{method.upper()} {path}"

        # Add parameter information
        parameters = operation.get('parameters', [])
        if parameters:
            desc += "\n\nParameters:"
            for param in parameters:
                param_name = param.get('name', 'unknown')
                param_desc = param.get('description', '')
                required = param.get('required', False)
                req_text = " (required)" if required else " (optional)"
                desc += f"\n- {param_name}{req_text}: {param_desc}"

        return desc

    def _extract_parameters(self, operation: Dict[str, Any], path: str) -> Dict[str, Any]:
        """Extract and categorize parameters from operation."""
        params: Dict[str, Any] = {
            'path': [],
            'query': [],
            'body': None
        }

        # Extract path parameters from the path string
        path_param_names = re.findall(r'\{([^}]+)\}', path)
        for param_name in path_param_names:
            params['path'].append({
                'name': param_name,
                'required': True,
                'type': 'string'  # Default to string
            })

        # Extract parameters from operation
        for param in operation.get('parameters', []):
            param_in = param.get('in', '')
            if param_in == 'path':
                # Update path parameter with more details
                param_name = param.get('name')
                for path_param in params['path']:
                    if path_param['name'] == param_name:
                        path_param.update({
                            'description': param.get('description', ''),
                            'type': self._get_param_type(param.get('schema', {}))
                        })
            elif param_in == 'query':
                params['query'].append({
                    'name': param.get('name'),
                    'required': param.get('required', False),
                    'description': param.get('description', ''),
                    'type': self._get_param_type(param.get('schema', {}))
                })

        # Extract request body
        request_body = operation.get('requestBody')
        if request_body:
            content = request_body.get('content', {})
            if 'application/json' in content:
                params['body'] = {
                    'required': request_body.get('required', False),
                    'schema': content['application/json'].get('schema', {})
                }

        return params

    def _get_param_type(self, schema: Dict[str, Any]) -> str:
        """Extract parameter type from schema."""
        return schema.get('type', 'string')

    def _map_parameters(self, path: str, parameters: Dict[str, Any], kwargs: Dict[str, Any]) -> Tuple[str, Dict[str, Any], Optional[Dict[str, Any]]]:
        """Map function kwargs to API call parameters."""
        # Handle path parameters
        path_with_params = path
        path_params = parameters.get('path', [])
        if isinstance(path_params, list):
            for path_param in path_params:  # type: ignore[misc]
                if isinstance(path_param, dict):
                    param_name = path_param.get('name')  # type: ignore[misc]
                    if param_name and param_name in kwargs:
                        path_with_params = path_with_params.replace(f'{{{param_name}}}', str(kwargs[param_name]))
                    elif param_name and path_param.get('required', False):
                        raise ValueError(f"Required path parameter '{param_name}' not provided")

        # Handle query parameters
        query_params: Dict[str, Any] = {}
        query_param_list = parameters.get('query', [])
        if isinstance(query_param_list, list):
            for query_param in query_param_list:  # type: ignore[misc]
                if isinstance(query_param, dict):
                    param_name = query_param.get('name')  # type: ignore[misc]
                    if param_name and param_name in kwargs:
                        query_params[param_name] = kwargs[param_name]
                    elif param_name and query_param.get('required', False):
                        raise ValueError(f"Required query parameter '{param_name}' not provided")

        # Handle request body
        json_data: Optional[Dict[str, Any]] = None
        body_config = parameters.get('body')
        if body_config:
            # Get parameter names that are already used
            used_param_names: set[str] = set()  # type: ignore[misc]
            if isinstance(path_params, list):
                used_param_names.update(p.get('name') for p in path_params if isinstance(p, dict) and p.get('name'))  # type: ignore[misc]
            if isinstance(query_param_list, list):
                used_param_names.update(p.get('name') for p in query_param_list if isinstance(p, dict) and p.get('name'))  # type: ignore[misc]

            # Remaining kwargs go into the body
            body_kwargs = {k: v for k, v in kwargs.items() if k not in used_param_names}
            if body_kwargs:
                json_data = body_kwargs
            elif isinstance(body_config, dict) and body_config.get('required', False):
                raise ValueError("Request body is required but no data provided")

        return path_with_params, query_params, json_data

    def _format_response(self, result: Any, operation_id: str) -> str:
        """Format API response for agent consumption."""
        if isinstance(result, dict):
            # For complex objects, provide a summary
            if len(result) == 0:  # type: ignore[misc]
                return f"{operation_id} completed successfully (no data returned)"

            # Try to extract key information
            summary_fields = ['id', 'name', 'title', 'status', 'count', 'total', 'success']
            summary: List[str] = []

            for field in summary_fields:
                if field in result:
                    summary.append(f"{field}: {result[field]}")

            if summary:
                response = f"{operation_id} result: " + ", ".join(summary)
            else:
                # Fallback to showing first few keys
                keys = [str(k) for k in list(result.keys())[:5]]  # type: ignore[misc]
                response = f"{operation_id} returned data with keys: {', '.join(keys)}"
                if len(result) > 5:  # type: ignore[misc]
                    response += f" (and {len(result) - 5} more)"  # type: ignore[misc]

            # Add full data as JSON for complex cases
            if len(str(result)) < 500:  # Only include full data if it's not too large  # type: ignore[misc]
                response += f"\n\nFull data: {result}"

            return response

        elif isinstance(result, list):
            count = len(result)  # type: ignore[misc]
            if count == 0:
                return f"{operation_id} returned empty list"
            elif count == 1:
                return f"{operation_id} returned 1 item: {result[0]}"
            else:
                return f"{operation_id} returned {count} items. First item: {result[0]}"

        else:
            return f"{operation_id} result: {result}"
