"""Shared authentication models for cross-service compatibility."""
from typing import Optional
from pydantic import BaseModel


class SessionInfo(BaseModel):
    """Session information returned by session validation."""
    
    issuer: str
    subject: str
    expiration_access_utc: str
    expiration_refresh_utc: str


class AuthContext(BaseModel):
    """Authentication context for validated sessions."""
    
    session_uuid: str
    subject: str
    issuer: str
    access_token: str
    session_info: Optional[SessionInfo] = None


class AuthConfig(BaseModel):
    """Configuration for authentication services."""
    
    runtime_service_url: str = "http://qw-mono-dev-runtime:8000"
    session_validation_timeout: float = 10.0
    session_cache_ttl: int = 300  # 5 minutes
