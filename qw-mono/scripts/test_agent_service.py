#!/usr/bin/env python3
"""Integration test script for FastAPI Agent Service."""
import asyncio
import json
import sys
import time
from typing import Dict, Any
import httpx


class AgentServiceTester:
    """Integration tester for the FastAPI Agent Service."""
    
    def __init__(self, base_url: str = "http://app.docker.localhost"):
        self.base_url = base_url
        self.session_token = None
        
    async def authenticate(self, username: str = "admin", password: str = "admin") -> bool:
        """
        Authenticate and get session token.
        This is a placeholder - replace with actual authentication logic.
        """
        print("🔐 Authenticating...")
        
        # For now, use a mock session token
        # In real implementation, this would call the login endpoint
        self.session_token = "mock-session-token"
        print("✅ Authentication successful")
        return True
    
    async def test_health_check(self) -> bool:
        """Test the health check endpoint."""
        print("🏥 Testing health check...")
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.base_url}/api/v1/agent/health")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ Health check passed: {data}")
                    return True
                else:
                    print(f"❌ Health check failed: {response.status_code}")
                    return False
                    
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False
    
    async def test_agent_prompt(self, prompt: str = "Hello, can you help me?") -> bool:
        """Test the agent prompt processing endpoint."""
        print(f"🤖 Testing agent prompt: '{prompt}'")
        
        if not self.session_token:
            print("❌ No session token available")
            return False
        
        try:
            request_data = {
                "prompt": prompt,
                "context": {"test": True}
            }
            
            start_time = time.time()
            
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    f"{self.base_url}/api/v1/agent/process-prompt",
                    json=request_data,
                    cookies={"session": self.session_token}
                )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Agent response received in {response_time:.2f}s")
                print(f"   Message: {data.get('message', 'No message')[:100]}...")
                print(f"   Session ID: {data.get('session_id')}")
                print(f"   Actions: {len(data.get('actions', []))}")
                return True
            else:
                print(f"❌ Agent request failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Agent request error: {e}")
            return False
    
    async def test_performance_baseline(self, num_requests: int = 5) -> Dict[str, float]:
        """Test performance baseline with multiple requests."""
        print(f"⚡ Testing performance baseline with {num_requests} requests...")
        
        if not self.session_token:
            print("❌ No session token available")
            return {}
        
        response_times = []
        successful_requests = 0
        
        for i in range(num_requests):
            print(f"   Request {i+1}/{num_requests}")
            
            try:
                request_data = {
                    "prompt": f"Test request {i+1}: What is quality management?",
                    "context": {"test": True, "request_id": i+1}
                }
                
                start_time = time.time()
                
                async with httpx.AsyncClient(timeout=60.0) as client:
                    response = await client.post(
                        f"{self.base_url}/api/v1/agent/process-prompt",
                        json=request_data,
                        cookies={"session": self.session_token}
                    )
                
                end_time = time.time()
                response_time = end_time - start_time
                
                if response.status_code == 200:
                    response_times.append(response_time)
                    successful_requests += 1
                    print(f"   ✅ Request {i+1} completed in {response_time:.2f}s")
                else:
                    print(f"   ❌ Request {i+1} failed: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ Request {i+1} error: {e}")
        
        if response_times:
            avg_time = sum(response_times) / len(response_times)
            min_time = min(response_times)
            max_time = max(response_times)
            
            print(f"📊 Performance Results:")
            print(f"   Successful requests: {successful_requests}/{num_requests}")
            print(f"   Average response time: {avg_time:.2f}s")
            print(f"   Min response time: {min_time:.2f}s")
            print(f"   Max response time: {max_time:.2f}s")
            
            return {
                "avg_time": avg_time,
                "min_time": min_time,
                "max_time": max_time,
                "success_rate": successful_requests / num_requests
            }
        
        return {}
    
    async def run_all_tests(self) -> bool:
        """Run all integration tests."""
        print("🚀 Starting FastAPI Agent Service Integration Tests")
        print("=" * 60)
        
        # Test authentication
        if not await self.authenticate():
            print("❌ Authentication failed - aborting tests")
            return False
        
        # Test health check
        if not await self.test_health_check():
            print("❌ Health check failed - aborting tests")
            return False
        
        # Test basic agent functionality
        if not await self.test_agent_prompt():
            print("❌ Agent prompt test failed")
            return False
        
        # Test performance baseline
        performance_results = await self.test_performance_baseline()
        if not performance_results:
            print("❌ Performance baseline test failed")
            return False
        
        print("=" * 60)
        print("✅ All tests completed successfully!")
        
        # Check performance thresholds
        if performance_results.get("avg_time", 0) > 30:
            print("⚠️  Warning: Average response time exceeds 30 seconds")
        
        if performance_results.get("success_rate", 0) < 0.8:
            print("⚠️  Warning: Success rate below 80%")
        
        return True


async def main():
    """Main test runner."""
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://app.docker.localhost"
    
    tester = AgentServiceTester(base_url)
    success = await tester.run_all_tests()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())
